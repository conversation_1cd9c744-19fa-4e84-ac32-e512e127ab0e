import request from "./request";
import { ReportData } from "@/types/api";

export const addAnnouncementApi = (data: {
    title: string;
    content: string;
    type: string;
    author: number;
    createTime: Date;
    readCount: number;
    status: string;
}): Promise<ReportData<any>> => {
    return request.post('/announcement/add', data)
}

export const getAnnouncementListApi = (data: {
    pageNum: number;
    pageSize: number;
    searchKey: string;
    title?: string;
    content?: string;
    type?: string;
    author?: number;
    createTime?: Date;
    readCount?: number;
    status?: string;
}): Promise<ReportData<any>> => {
    return request.post('/announcement/pageList', data)
}

export const getAllAnnouncementListApi = (data: {
    searchKey: string;
    title?: string;
    content?: string;
    type?: string;
    author?: number;
    createTime?: Date;
    readCount?: number;
    status?: string;
}): Promise<ReportData<any>> => {
    return request.post('/announcement/list', data)
}

export const editAnnouncementApi = (data: {
    announcementId: number;
    title: string;
    content: string;
    type: string;
    author: number;
    createTime: Date;
    readCount: number;
    status: string;
}): Promise<ReportData<any>> => {
    return request.put('/announcement/edit', data)
}

export const deleteAnnouncementApi = (announcementId: number): Promise<ReportData<any>> => {
    return request.delete(`/announcement/delete/${announcementId}`)
}

export const getAnnouncementByIdApi = (announcementId: number): Promise<ReportData<any>> => {
    return request.get(`/announcement/getById/${announcementId}`)
}
/announcement/