<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { Search, Plus, Edit, Delete, View, Message, Check, Clock, ArrowDown } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import BackHeader from '@/components/BackHeader.vue';
import {
  addAnnouncementApi,
  getAnnouncementListApi,
  editAnnouncementApi,
  deleteAnnouncementApi,
  getAnnouncementByIdApi
} from '@/request/announcementsApi';

// 搜索关键词
const searchKeyword = ref('');

// 当前页码
const currentPage = ref(1);
// 每页显示条数
const pageSize = ref(10);
// 总条数
const total = ref(0);

// 加载状态
const loading = ref(false);

// 公告统计数据
const announcementStats = reactive([
  { label: '公告总数', count: 0, icon: 'Message' },
  { label: '已发布', count: 0, icon: 'Check' },
  { label: '草稿', count: 0, icon: 'Clock' },
  { label: '总阅读量', count: 0, icon: 'View' }
]);

// 公告数据
const announcements = ref([]);

// 筛选条件
const filterValue = ref('全部类型');
const selectedTypeValue = ref(''); // 存储实际的数值用于API调用
const typeOptions = [
  { label: '全部类型', value: '' },
  { label: '通知', value: '0' },
  { label: '紧急通知', value: '1' },
  { label: '活动', value: '2' },
  { label: '公告', value: '3' }
];

// 状态选项
const statusOptions = [
  { label: '草稿', value: '0' },
  { label: '已发布', value: '1' }
];

// 添加公告对话框
const addDialogVisible = ref(false);
const addFormRef = ref();
const newAnnouncement = reactive({
  title: '',
  content: '',
  type: '0', // 0通知
  status: '1' // 1已发布
});

// 编辑公告对话框
const editDialogVisible = ref(false);
const editFormRef = ref();
const editAnnouncement = reactive({
  announcementId: '',
  title: '',
  content: '',
  type: '0', // 0通知
  status: '1' // 1已发布
});

// 查看详情对话框
const viewDialogVisible = ref(false);
const viewAnnouncementData = reactive({});

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' },
    { min: 10, max: 2000, message: '内容长度在 10 到 2000 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择公告类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择发布状态', trigger: 'change' }
  ]
};

// 锁定页面滚动
const lockBodyScroll = () => {
  document.body.style.overflow = 'hidden';
};

// 解锁页面滚动
const unlockBodyScroll = () => {
  document.body.style.overflow = '';
};



// 加载公告列表
const loadAnnouncementList = async () => {
  try {
    loading.value = true;
    const response = await getAnnouncementListApi({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      searchKey: searchKeyword.value,
      type: selectedTypeValue.value
    });

    if (response.code === 200 && response.data) {

      // 尝试不同的数据结构
      let listData = [];
      let totalCount = 0;

      if (response.data.records) {
        // 分页数据结构：{ records: [], total: 0 }
        listData = response.data.records;
        totalCount = response.data.total || 0;
      } else if (response.data.list) {
        // 列表数据结构：{ list: [], total: 0 }
        listData = response.data.list;
        totalCount = response.data.total || 0;
      } else if (Array.isArray(response.data)) {
        // 直接数组结构：[]
        listData = response.data;
        totalCount = response.total;
      } else {
        console.warn('未知的数据结构:', response.data);
      }

      console.log('解析后的列表数据:', listData); // 调试日志

      announcements.value = listData;
      total.value = totalCount;

      // 更新统计数据
      await updateStats();
    } else {
      console.error('API返回错误:', response);
      ElMessage.error(response.msg || '获取公告列表失败');
    }
  } catch (error) {
    console.error('获取公告列表失败:', error);
    ElMessage.error('获取公告列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 更新统计数据 - 获取所有数据进行统计
const updateStats = async () => {
  try {
    // 获取所有公告数据用于统计
    const response = await getAnnouncementListApi({
      pageNum: 1,
      pageSize: 1000, // 获取大量数据用于统计
      searchKey: '',
      type: ''
    });

    if (response.code === 200 && response.data && response.data.records) {
      const allAnnouncements = response.data.records;
      const total = allAnnouncements.length;
      const published = allAnnouncements.filter(item => item.status === '1' || item.status === 1).length;
      const draft = allAnnouncements.filter(item => item.status === '0' || item.status === 0).length;
      const totalReads = allAnnouncements.reduce((sum, item) => sum + (item.readCount || 0), 0);

      announcementStats[0].count = total;
      announcementStats[1].count = published;
      announcementStats[2].count = draft;
      announcementStats[3].count = totalReads;
    }
  } catch (error) {
    console.error('更新统计数据失败:', error);
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadAnnouncementList();
};

// 处理筛选
const handleFilter = (value) => {
  // 找到对应的选项来显示标签
  const selectedOption = typeOptions.find(option => option.value === value);
  filterValue.value = selectedOption ? selectedOption.label : '全部类型';
  selectedTypeValue.value = value; // 存储实际的数值用于API调用
  currentPage.value = 1;
  loadAnnouncementList();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadAnnouncementList();
};

// 获取类型标签
const getTypeLabel = (type) => {
  const typeMap = {
    0: '通知',
    1: '紧急通知',
    2: '活动',
    3: '公告'
  };
  return typeMap[type] || '未知类型';
};

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    0: '草稿',
    1: '已发布'
  };
  return statusMap[status] || '未知状态';
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 添加新公告
const addNewAnnouncement = async () => {
  addDialogVisible.value = true;
  lockBodyScroll();

  // 重置表单
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  Object.assign(newAnnouncement, {
    title: '',
    content: '',
    type: '0', // 0通知
    status: '1' // 1已发布
  });
};

// 提交新公告
const submitNewAnnouncement = async () => {
  if (!addFormRef.value) return;

  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;

        const response = await addAnnouncementApi({
          title: newAnnouncement.title,
          content: newAnnouncement.content,
          type: newAnnouncement.type,
          author: 1, // 默认作者ID，可以根据当前登录用户设置
          readCount: 0,
          status: newAnnouncement.status
        });

        if (response.code === 200) {
          ElMessage.success('添加公告成功');
          addDialogVisible.value = false;
          unlockBodyScroll();
          await loadAnnouncementList();
        } else {
          ElMessage.error(response.msg || '添加公告失败');
        }
      } catch (error) {
        console.error('添加公告失败:', error);
        ElMessage.error('添加公告失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 关闭添加对话框
const closeAddDialog = () => {
  addDialogVisible.value = false;
  unlockBodyScroll();
};

// 编辑公告
const editAnnouncementHandler = async (announcement) => {
  try {
    const response = await getAnnouncementByIdApi(announcement.announcementId || announcement.id);
    if (response.code === 200 && response.data) {
      Object.assign(editAnnouncement, {
        announcementId: response.data.announcementId || response.data.id,
        title: response.data.title,
        content: response.data.content,
        type: String(response.data.type), // 确保是字符串格式
        status: String(response.data.status) // 确保是字符串格式
      });

      editDialogVisible.value = true;
      lockBodyScroll();
    } else {
      ElMessage.error('获取公告详情失败');
    }
  } catch (error) {
    console.error('获取公告详情失败:', error);
    ElMessage.error('获取公告详情失败，请稍后重试');
  }
};

// 提交编辑公告
const submitEditAnnouncement = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;

        const response = await editAnnouncementApi({
          announcementId: editAnnouncement.announcementId,
          title: editAnnouncement.title,
          content: editAnnouncement.content,
          type: editAnnouncement.type,
          author: 1, // 默认作者ID，可以根据当前登录用户设置
          readCount: 0,
          status: editAnnouncement.status
        });

        if (response.code === 200) {
          ElMessage.success('编辑公告成功');
          editDialogVisible.value = false;
          unlockBodyScroll();
          await loadAnnouncementList();
        } else {
          ElMessage.error(response.msg || '编辑公告失败');
        }
      } catch (error) {
        console.error('编辑公告失败:', error);
        ElMessage.error('编辑公告失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 关闭编辑对话框
const closeEditDialog = () => {
  editDialogVisible.value = false;
  unlockBodyScroll();
};

// 删除公告
const deleteAnnouncementHandler = async (announcement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除公告"${announcement.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const response = await deleteAnnouncementApi(announcement.announcementId || announcement.id);
    if (response.code === 200) {
      ElMessage.success('删除公告成功');
      await loadAnnouncementList();
    } else {
      ElMessage.error(response.msg || '删除公告失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除公告失败:', error);
      ElMessage.error('删除公告失败，请稍后重试');
    }
  }
};

// 查看公告详情
const viewAnnouncementDetail = async (announcement) => {
  try {
    const response = await getAnnouncementByIdApi(announcement.announcementId || announcement.id);
    if (response.code === 200 && response.data) {
      Object.assign(viewAnnouncementData, {
        ...response.data,
        typeLabel: getTypeLabel(response.data.type),
        statusLabel: getStatusLabel(response.data.status),
        createTimeFormatted: formatDate(response.data.createTime)
      });
      viewDialogVisible.value = true;
      lockBodyScroll();
    } else {
      ElMessage.error('获取公告详情失败');
    }
  } catch (error) {
    console.error('获取公告详情失败:', error);
    ElMessage.error('获取公告详情失败，请稍后重试');
  }
};

// 关闭查看详情对话框
const closeViewDialog = () => {
  viewDialogVisible.value = false;
  unlockBodyScroll();
};

// 页面初始化
onMounted(async () => {
  await loadAnnouncementList();
});
</script>

<template>
  <BackHeader activeMenu="announcements">
    <div class="announcements-container">
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">公告通知</h1>
          <p class="page-subtitle">发布和管理小区公告通知</p>
        </div>

        <el-button type="primary" class="add-announcement-btn" @click="addNewAnnouncement">
          <el-icon><Plus /></el-icon>
          发布公告
        </el-button>
      </div>

      <!-- 公告统计卡片 -->
      <div class="announcement-stats">
        <div v-for="(stat, index) in announcementStats" :key="index" class="stat-card">
          <div class="stat-icon-container" :class="`stat-icon-${index}`">
            <el-icon v-if="index === 0"><Message /></el-icon>
            <el-icon v-else-if="index === 1"><Check /></el-icon>
            <el-icon v-else-if="index === 2"><Clock /></el-icon>
            <el-icon v-else><View /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-count">{{ stat.count }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
              v-model="searchKeyword"
              placeholder="搜索公告标题或内容..."
              class="search-input"
              @keyup.enter="handleSearch"
              clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="handleSearch" class="search-btn">
            搜索
          </el-button>
        </div>

        <div class="filter-box">
          <el-dropdown @command="handleFilter">
            <el-button class="filter-button">
              {{ filterValue }}
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="option in typeOptions" :key="option.value" :command="option.value">
                  {{ option.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 公告列表 -->
      <div class="announcements-list" v-loading="loading">
        <div v-if="announcements.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无公告数据" />
        </div>
        <div v-else>
          <div v-for="announcement in announcements" :key="announcement.announcementId || announcement.id" class="announcement-card">
            <div class="announcement-header">
              <div class="announcement-title-section">
                <div class="announcement-type-tag" :class="(announcement.type === '1' || announcement.type === 1) ? 'urgent' : 'normal'">
                  {{ getTypeLabel(announcement.type) }}
                </div>
                <h3 class="announcement-title">{{ announcement.title }}</h3>
              </div>

              <div class="announcement-status">
                <el-tag :type="(announcement.status === '1' || announcement.status === 1) ? 'success' : 'info'" size="small">
                  {{ getStatusLabel(announcement.status) }}
                </el-tag>
              </div>
            </div>

            <div class="announcement-content">
              {{ announcement.content }}
            </div>

            <div class="announcement-footer">
              <div class="announcement-info">
                <div class="info-item">
                  <span class="info-label">创建时间:</span>
                  <span class="info-value">{{ formatDate(announcement.createTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">阅读量:</span>
                  <span class="info-value">{{ announcement.readCount || 0 }}</span>
                </div>
              </div>

              <div class="announcement-actions">
                <el-button type="primary" size="default" text @click="viewAnnouncementDetail(announcement)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button type="primary" size="default" text @click="editAnnouncementHandler(announcement)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="default" text @click="deleteAnnouncementHandler(announcement)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="announcements.length > 0">
        <div class="total-info">共 {{ total }} 条记录</div>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加公告对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="发布公告"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeAddDialog"
      class="announcement-dialog"
    >
      <el-form
        ref="addFormRef"
        :model="newAnnouncement"
        :rules="formRules"
        label-width="80px"
        class="announcement-form"
      >
        <el-form-item label="公告标题" prop="title">
          <el-input
            v-model="newAnnouncement.title"
            placeholder="请输入公告标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="公告类型" prop="type">
          <el-select v-model="newAnnouncement.type" placeholder="请选择公告类型" style="width: 100%">
            <el-option label="通知" value="0" />
            <el-option label="紧急通知" value="1" />
            <el-option label="活动" value="2" />
            <el-option label="公告" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="发布状态" prop="status">
          <el-select v-model="newAnnouncement.status" placeholder="请选择发布状态" style="width: 100%">
            <el-option label="草稿" value="0" />
            <el-option label="已发布" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="公告内容" prop="content">
          <el-input
            v-model="newAnnouncement.content"
            type="textarea"
            :rows="6"
            placeholder="请输入公告内容"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeAddDialog">取消</el-button>
          <el-button type="primary" @click="submitNewAnnouncement" :loading="loading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑公告对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑公告"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeEditDialog"
      class="announcement-dialog"
    >
      <el-form
        ref="editFormRef"
        :model="editAnnouncement"
        :rules="formRules"
        label-width="80px"
        class="announcement-form"
      >
        <el-form-item label="公告标题" prop="title">
          <el-input
            v-model="editAnnouncement.title"
            placeholder="请输入公告标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="公告类型" prop="type">
          <el-select v-model="editAnnouncement.type" placeholder="请选择公告类型" style="width: 100%">
            <el-option label="通知" value="0" />
            <el-option label="紧急通知" value="1" />
            <el-option label="活动" value="2" />
            <el-option label="公告" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="发布状态" prop="status">
          <el-select v-model="editAnnouncement.status" placeholder="请选择发布状态" style="width: 100%">
            <el-option label="草稿" value="0" />
            <el-option label="已发布" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="公告内容" prop="content">
          <el-input
            v-model="editAnnouncement.content"
            type="textarea"
            :rows="6"
            placeholder="请输入公告内容"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeEditDialog">取消</el-button>
          <el-button type="primary" @click="submitEditAnnouncement" :loading="loading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看公告详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="公告详情"
      width="600px"
      @close="closeViewDialog"
      class="announcement-dialog"
    >
      <div class="announcement-detail">
        <div class="detail-header">
          <div class="detail-title">{{ viewAnnouncementData.title }}</div>
          <el-tag :type="viewAnnouncementData.status === '已发布' ? 'success' : 'info'" size="small">
            {{ viewAnnouncementData.status }}
          </el-tag>
        </div>

        <div class="detail-meta">
          <div class="meta-item">
            <span class="meta-label">公告类型：</span>
            <span class="meta-value">{{ viewAnnouncementData.typeLabel }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">发布状态：</span>
            <span class="meta-value">{{ viewAnnouncementData.statusLabel }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">创建时间：</span>
            <span class="meta-value">{{ viewAnnouncementData.createTimeFormatted }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">阅读量：</span>
            <span class="meta-value">{{ viewAnnouncementData.readCount || 0 }}</span>
          </div>
        </div>

        <div class="detail-content">
          <div class="content-label">公告内容：</div>
          <div class="content-text">{{ viewAnnouncementData.content }}</div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeViewDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </BackHeader>
</template>

<style scoped>
.announcements-container {
  padding: 0 20px 20px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 10px 0 0 0;
}

.add-announcement-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 48px;
  font-size: 16px;
  padding: 0 20px;
}

.announcement-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  flex: 1;
  background-color: #fff;
  border-radius: 12px;
  padding: 28px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.stat-icon-container {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-icon-container .el-icon {
  font-size: 32px;
}

.stat-icon-0 {
  background-color: #e3f2fd;
  color: #1976d2;
}

.stat-icon-1 {
  background-color: #e8f5e8;
  color: #4caf50;
}

.stat-icon-2 {
  background-color: #fff3e0;
  color: #ff9800;
}

.stat-icon-3 {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-count {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #909399;
}

.search-filter-bar {
  display: flex;
  margin-bottom: 32px;
  gap: 16px;
}

.search-box {
  flex: 1;
  display: flex;
  gap: 12px;
}

.search-input {
  flex: 1;
}

.search-input :deep(.el-input__inner) {
  height: 40px;
  font-size: 14px;
}

.search-btn {
  height: 40px;
  font-size: 14px;
  padding: 0 16px;
}

.filter-button {
  height: 40px;
  font-size: 14px;
  padding: 0 16px;
}

.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.announcement-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.announcement-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.announcement-type-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.announcement-type-tag.urgent {
  background-color: #fef0f0;
  color: #f56c6c;
}

.announcement-type-tag.normal {
  background-color: #f0f9eb;
  color: #67c23a;
}

.announcement-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.announcement-tags {
  display: flex;
  gap: 8px;
}

.tag {
  font-size: 12px;
}

.announcement-content {
  font-size: 15px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.announcement-info {
  display: flex;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.info-label {
  color: #909399;
}

.info-value {
  color: #606266;
}

.announcement-publish-info {
  font-size: 14px;
}

.publish-label {
  color: #909399;
}

.publish-time {
  color: #606266;
}

.announcement-actions {
  display: flex;
  gap: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
}

.total-info {
  color: #606266;
  font-size: 14px;
}

/* 对话框样式 */
.announcement-dialog :deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
}

.announcement-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.announcement-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px 20px;
  border-top: 1px solid #ebeef5;
}

.announcement-form {
  max-height: 60vh;
  overflow-y: auto;
}

.announcement-form .el-form-item {
  margin-bottom: 20px;
}

.announcement-form .el-form-item__label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 查看详情样式 */
.announcement-detail {
  font-size: 14px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.detail-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16px;
}

.detail-meta {
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  margin-bottom: 12px;
}

.meta-label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.meta-value {
  color: #606266;
  flex: 1;
}

.detail-content {
  margin-top: 20px;
}

.content-label {
  color: #909399;
  margin-bottom: 12px;
  font-weight: 500;
}

.content-text {
  color: #606266;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .announcements-container {
    padding: 0 16px 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .announcement-stats {
    flex-direction: column;
    gap: 16px;
  }

  .search-filter-bar {
    flex-direction: column;
    gap: 12px;
  }

  .announcement-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}
</style>